const _ = require("lodash");
const express = require("express");
const { exec, spawn } = require("child_process");
const dotenv = require("dotenv");

// 加载环境变量
dotenv.config({ path: './config.env' });

// 请求队列管理
class RequestQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.maxConcurrent = 3; // 最大并发数
    this.currentRunning = 0;
  }

  // 添加请求到队列
  add(task) {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject });
      this.processQueue();
    });
  }

  // 处理队列
  async processQueue() {
    if (this.processing || this.currentRunning >= this.maxConcurrent) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0 && this.currentRunning < this.maxConcurrent) {
      const { task, resolve, reject } = this.queue.shift();
      this.currentRunning++;

      try {
        const result = await task();
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        this.currentRunning--;
      }
    }

    this.processing = false;

    // 如果队列还有任务且未达到最大并发，继续处理
    if (this.queue.length > 0 && this.currentRunning < this.maxConcurrent) {
      this.processQueue();
    }
  }

  // 获取队列状态
  getStatus() {
    return {
      queueLength: this.queue.length,
      currentRunning: this.currentRunning,
      maxConcurrent: this.maxConcurrent
    };
  }
}

// 创建全局请求队列
const requestQueue = new RequestQueue();

const app = express();
const ip = process.env.IP || "0.0.0.0";
const port = process.env.PORT || 3000;
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const axios = require("axios");

// 允许跨域请求
const cors = require("cors");
// app.use(cors());

// 用于解析 JSON 请求
app.use(express.json());

const tiktokdll = require("./index");
const Tiktok = require("@tobyg74/tiktok-api-dl");

const COOKIE = require("./cookie.js");
const { getSettings, saveSettings } = require("./database");

// 从环境变量读取固定的认证 token
const AUTH_TOKEN = process.env.AUTH_TOKEN;

// 验证函数 - 直接比对固定的 token
function verifyAuthToken(token) {
  if (!token || !AUTH_TOKEN) return false;
  
  // 直接比对 token
  return token === AUTH_TOKEN;
}

// 提取上传者名字
function extractUploaderName(url) {
  const match = url.match(/https:\/\/www\.tiktok\.com\/@([^\/\?]+)/);
  if (match && match[1]) {
    return match[1]; // 提取到的上传者名字
  }
  return null; // 无法匹配时返回 null
}

// 从HTML内容中提取TikTok用户头像
function extractTikTokAvatar(htmlContent) {
  try {
    // 查找包含用户信息的JSON数据
    const scriptTagRegex =
      /<script id="__UNIVERSAL_DATA_FOR_REHYDRATION__" type="application\/json">(.*?)<\/script>/s;
    const scriptMatch = htmlContent.match(scriptTagRegex);
    if (!scriptMatch || !scriptMatch[1]) {
      throw new Error("无法找到用户数据JSON");
    }
    // 解析JSON数据
    const jsonData = JSON.parse(scriptMatch[1]);
    // 提取用户信息
    const userInfo =
      jsonData["__DEFAULT_SCOPE__"]["webapp.user-detail"]?.userInfo?.user;
    if (!userInfo) {
      throw new Error("无法找到用户信息");
    }
    // 提取不同尺寸的头像URL
    const avatars = {
      large: userInfo.avatarLarger,
      medium: userInfo.avatarMedium,
      thumb: userInfo.avatarThumb,
    };
    return avatars;
  } catch (error) {
    console.error("提取头像失败:", error.message);
    return null;
  }
}

// 获取用户头像
app.post("/get-avatar", async (req, res) => {
  console.log("get-avatar");
  const name = req.body.name; // 客户端发送的 TikTok 用户名或URL
  console.log(`name: ${name}`);

  if (!name) {
    return res.status(400).json({ error: "Missing name" });
  }

  // 提取用户名，如果传入的是完整URL则提取用户名部分
  let username = name;
  if (name.includes('tiktok.com/@')) {
    const match = name.match(/tiktok\.com\/@([^\/\?]+)/);
    if (match && match[1]) {
      username = match[1];
    }
  }

  console.log(`提取的用户名: ${username}`);

  try {
    // 请求用户的TikTok页面
    const url = `https://www.tiktok.com/@${username}`;
    console.log(`请求用户页面: ${url}`);

    const response = await axios.get(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9",
      },
    });

    // 提取头像URL
    const avatars = extractTikTokAvatar(response.data);

    if (!avatars) {
      return res.status(404).json({ error: "Could not extract avatar URLs" });
    }

    console.log(`成功获取 ${name} 的头像:`, avatars);
    return res.json(avatars);
  } catch (error) {
    console.error(`获取头像时出错:`, error);
    return res.status(500).json({
      error: "Failed to fetch avatar",
      details: error.message,
      name: name,
    });
  }
});

// 获取用户的总帖子数和总页数
app.post("/get-pages", (req, res) => {
  console.log("get-pages");
  const name = req.body.name; // 客户端发送的 TikTok 用户名或URL
  console.log(`name: ${name}`);

  if (!name) {
    return res.status(400).json({ error: "Missing name" });
  }

  // 提取用户名，如果传入的是完整URL则提取用户名部分
  let username = name;
  if (name.includes('tiktok.com/@')) {
    const match = name.match(/tiktok\.com\/@([^\/\?]+)/);
    if (match && match[1]) {
      username = match[1];
    }
  }

  console.log(`提取的用户名: ${username}`);

  // 获取用户总共有多少条视频 - 直接使用 yt-dlp 的 playlist_count 输出
  const countCommand = `yt-dlp --flat-playlist --skip-download --print playlist_count https://www.tiktok.com/@${username}`;

  console.log(`执行命令获取总数: ${countCommand}`);

  exec(countCommand, (countError, countStdout, countStderr) => {
    if (countError) {
      console.error(`获取视频总数时出错: ${countError}`);
      return res
        .status(500)
        .json({ error: "Failed to fetch total posts count" });
    }

    if (countStderr) {
      console.error(`命令错误输出: ${countStderr}`);
    }

    let totalPosts = 0;
    if (countStdout) {
      // 获取输出的第一行，并转换为整数
      const firstLine = countStdout.toString().trim().split('\n')[0];
      totalPosts = parseInt(firstLine) || 0;
      console.log(`用户 ${username} 总共有 ${totalPosts} 条视频`);
    }

    // 计算总页数
    const itemsPerPage = 9;
    const totalPages = Math.ceil(totalPosts / itemsPerPage);

    // 返回分页信息
    return res.json({
      name: username,
      totalPosts: totalPosts,
      totalPages: totalPages,
      itemsPerPage: itemsPerPage,
    });
  });
});

app.post("/get-post", (req, res) => {
  console.log("get-post");
  const name = req.body.name; // 客户端发送的 TikTok 用户名或URL
  const page = parseInt(req.body.page) || 1; // 获取页码，默认为1
  console.log(`name: ${name}, page: ${page}`);

  if (!name) {
    return res.status(400).json({ error: "Missing name" });
  }

  // 提取用户名，如果传入的是完整URL则提取用户名部分
  let username = name;
  if (name.includes('tiktok.com/@')) {
    const match = name.match(/tiktok\.com\/@([^\/\?]+)/);
    if (match && match[1]) {
      username = match[1];
    }
  }

  console.log(`提取的用户名: ${username}`);

  // 计算要获取的视频范围
  const itemsPerPage = 9;
  const startItem = (page - 1) * itemsPerPage + 1;
  const endItem = page * itemsPerPage;
  const playlistItems = `${startItem}-${endItem}`;

  // 使用 yt-dlp 获取用户视频数据，根据页码获取对应范围的视频
  const command = `yt-dlp --flat-playlist --playlist-items ${playlistItems} --dump-json https://www.tiktok.com/@${username}`;

  console.log(`执行命令: ${command}`);

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`执行命令时出错: ${error}`);
      return res
        .status(500)
        .json({ error: "Failed to fetch data using yt-dlp" });
    }

    if (stderr) {
      console.error(`命令错误输出: ${stderr}`);
    }

    try {
      // 将每行JSON解析为对象并存入数组
      const jsonLines = stdout.trim().split("\n");

      // 如果没有输出，返回空数组
      if (!jsonLines[0]) {
        console.log(`页码 ${page} 没有数据`);
        return res.json([]);
      }

      const posts = jsonLines.map((line) => JSON.parse(line));

      console.log(`页码 ${page} 获取到 ${posts.length} 个视频`);
      console.log("Source result from yt-dlp", posts);

      if (posts.length !== 0) {
        const format_result = posts
          .filter((item) => item) // 确保项目存在
          .map((item) => {
            // 安全地访问可能不存在的属性
            const thumbnail =
              item.thumbnails && item.thumbnails.length > 0
                ? item.thumbnails[0].url
                : "";

            return {
              id: item.id,
              desc: item.title || item.description || "",
              cover: thumbnail || "",
              url:
                item.webpage_url ||
                `https://www.tiktok.com/@${username}/video/${item.id}`,
              userid: item.uploader_id || "",
              name: item.uploader || username,
              avatar: "",
              timestamp: item.timestamp || Math.floor(Date.now() / 1000),
              timesread: item.timestamp
                ? new Date(item.timestamp * 1000).toLocaleString()
                : new Date().toLocaleString(),
              page: page, // 添加页码信息
            };
          })
          .sort((a, b) => b.timestamp - a.timestamp); // 按照 timestamp 倒序排序

        console.log("Format result", format_result);
        return res.json(format_result);
      } else {
        console.log("Method failed: can't get posts from yt-dlp");
        return res.json([]);
      }
    } catch (e) {
      console.error("解析 yt-dlp 输出时出错:", e);
      console.error(e.stack);
      return res
        .status(500)
        .json({ error: "Failed to parse yt-dlp output", details: e.message });
    }
  });

  /* 以下是原来的代码，已注释掉但保留
  // 尝试第一个方法
  // proxy: "http://127.0.0.1:8080", // Support Proxy Http, Https, Socks5
  Tiktok.StalkUser(name, {
    cookie: COOKIE,
    postLimit: 999, // Limit the number of posts to display
  }).then((result) => {
    console.log("Source result", result.result.posts);
    if (result.result.posts.length !== 0) {
      const format_result = result.result.posts
        .filter((item) => item.video) // 只保留有 video 字段的对象
        .map((item) => ({
          id: item.id,
          desc: item.desc,
          cover: item.video.cover,
          url: `https://www.tiktok.com/@${name}/video/${item.id}`,
          userid: item.author.id,
          name: item.author.nickname,
          avatar: item.author.avatarMedium,
          timestamp: item.createTime,
          timesread: new Date(item.createTime * 1000).toLocaleString(),
        }))
        .sort((a, b) => b.timestamp - a.timestamp); // 按照 timestamp 倒序排序
      console.log("Format result", format_result);
      return res.json(format_result);
    } else {
      console.log("Method 1 failed: can't get posts", result.result.posts);
      return res.json("failed: can't get posts now");
    }
  });
  */
});

app.post("/get-url", async (req, res) => {
  console.log("get-url");
  const videoUrl = req.body.url; // 客户端发送的 TikTok 视频 URL
  console.log(videoUrl);
  if (!videoUrl) {
    return res.status(400).json({ error: "Missing video URL" });
  }

  // try {
  //   // 尝试第一个方法
  //   const result1 = await tiktokdll.tiktokdownload(videoUrl);
  //   console.log("Method 1", result1);
  //   return res.json({ links: [result1.nowm] });
  // } catch (e) {
  //   console.log("Method 1 failed:", e);
  // }

  try {
    // 尝试第二个方法
    const result2 = await Tiktok.Downloader(videoUrl, {
      version: "v1", //  version: "v1" | "v2" | "v3"
      showOriginalResponse: true, // Only for V1
    });
    console.log("Method 2", result2);
    return res.json({
      links:
        result2.resultNotParsed.content.video.bit_rate[0].play_addr.url_list,
    });
  } catch (e) {
    console.log("Method 2 failed:", e);
  }

  try {
    // 尝试第三个方法
    const { tikdown } = require("nayan-videos-downloader");
    const result3 = await tikdown(videoUrl);
    console.log("Method 3", result3);
    return res.json({
      links: result3.data.video,
    });
  } catch (e) {
    console.log("Method 3 failed:", e);
    return res.status(500).json({ error: "All methods failed" });
  }
});

// GET /setting - 获取设置
app.get("/setting", (req, res) => {
  console.log("GET /setting");
  
  try {
    const settings = getSettings();
    if (settings) {
      return res.json(settings);
    } else {
      return res.json({ message: "No settings found" });
    }
  } catch (error) {
    console.error("Error getting settings:", error);
    return res.status(500).json({ error: "Failed to get settings" });
  }
});

// POST /setting - 保存设置（需要验证）
app.post("/setting", (req, res) => {
  console.log("POST /setting");
  
  // 检查认证头
  const authToken = req.headers['authorization'] || req.headers['auth-token'];
  
  if (!verifyAuthToken(authToken)) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  
  // 检查请求体
  if (!req.body || typeof req.body !== 'object') {
    return res.status(400).json({ error: "Invalid request body" });
  }
  
  try {
    // 保存设置
    saveSettings(req.body);
    return res.json({ success: true, message: "Settings saved successfully" });
  } catch (error) {
    console.error("Error saving settings:", error);
    return res.status(500).json({ error: "Failed to save settings" });
  }
});

// 启动 Express 服务器
app.listen(port, ip, () => {
  console.log(`Server running at http://${ip}:${port}`);
  
  // 显示认证 token（仅用于开发环境）
  if (process.env.NODE_ENV !== 'production' && AUTH_TOKEN) {
    console.log(`Auth token: ${AUTH_TOKEN}`);
    console.log(`Use this token in the 'Authorization' header for POST /setting`);
  } else if (!AUTH_TOKEN) {
    console.warn('Warning: AUTH_TOKEN not configured in config.env');
  }
});
