const Database = require('better-sqlite3');
const path = require('path');

// 创建或连接到数据库
const db = new Database(path.join(__dirname, 'settings.db'));

// 创建设置表（如果不存在）
db.exec(`
  CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY DEFAULT 1,
    data TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`);

// 获取设置
function getSettings() {
  const row = db.prepare('SELECT data FROM settings WHERE id = 1').get();
  if (row) {
    try {
      return JSON.parse(row.data);
    } catch (e) {
      return null;
    }
  }
  return null;
}

// 保存设置
function saveSettings(data) {
  const jsonData = JSON.stringify(data);
  const existing = db.prepare('SELECT id FROM settings WHERE id = 1').get();
  
  if (existing) {
    db.prepare('UPDATE settings SET data = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1').run(jsonData);
  } else {
    db.prepare('INSERT INTO settings (id, data) VALUES (1, ?)').run(jsonData);
  }
  
  return true;
}

module.exports = {
  getSettings,
  saveSettings
};