const axios = require('axios');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config({ path: './config.env' });

// 服务器地址
const SERVER_URL = 'http://localhost:3000';

// 从环境变量读取固定的认证 token
const authToken = process.env.AUTH_TOKEN;

console.log('Auth Token:', authToken);

// 测试 POST 请求 - 保存设置
async function testPostSetting() {
  try {
    const response = await axios.post(`${SERVER_URL}/setting`, {
      theme: 'dark',
      language: 'zh-CN',
      maxDownloads: 10,
      enableNotifications: true,
      customSettings: {
        videoQuality: 'high',
        saveLocation: './downloads'
      }
    }, {
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('POST /setting response:', response.data);
  } catch (error) {
    console.error('POST /setting error:', error.response?.data || error.message);
  }
}

// 测试 GET 请求 - 获取设置
async function testGetSetting() {
  try {
    const response = await axios.get(`${SERVER_URL}/setting`);
    console.log('GET /setting response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('GET /setting error:', error.response?.data || error.message);
  }
}

// 测试未授权的 POST 请求
async function testUnauthorizedPost() {
  try {
    const response = await axios.post(`${SERVER_URL}/setting`, {
      test: 'data'
    }, {
      headers: {
        'Authorization': 'wrong-token',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Unauthorized POST response:', response.data);
  } catch (error) {
    console.error('Unauthorized POST error (expected):', error.response?.data || error.message);
  }
}

// 运行测试
async function runTests() {
  console.log('=== 测试 /setting API ===\n');
  
  console.log('1. 测试未授权的 POST 请求:');
  await testUnauthorizedPost();
  
  console.log('\n2. 测试授权的 POST 请求:');
  await testPostSetting();
  
  console.log('\n3. 测试 GET 请求:');
  await testGetSetting();
}

// 确保服务器已启动
console.log('请确保服务器已在 http://localhost:3000 上运行');
console.log('使用 "node app.js" 启动服务器\n');

// 延迟一秒后运行测试，给用户时间阅读提示
setTimeout(runTests, 1000);