const express = require("express");
const { exec } = require("child_process");


const app = express();

// 允许跨域请求
const cors = require("cors");
// app.use(cors());

// 用于解析 JSON 请求
app.use(express.json());

// POST 路由：接收视频 URL，返回下载链接
app.post("/get-link", async (req, res) => {
  // return
  const { url } = req.body;
  console.log(url);

  if (!url) {
    return res.status(400).send({ error: "URL is required" });
  }

  try {
    const result = await youtubedl.exec(
      url,
      { getUrl: true, printTraffic: true },
      {
        timeout: 5000,
        killSignal: "SIGKILL",
      }
    );

    console.log(result);
    res.send({ result });
  } catch (error) {
    console.error(error); // 打印详细的错误信息，帮助调试
    res
      .status(500)
      .send({ error: "Failed to fetch video info", details: error.message });
  }
});

app.post("/get-download-url", (req, res) => {
  const videoUrl = req.body.url; // 客户端发送的 TikTok 视频 URL
  console.log(videoUrl);
  if (!videoUrl) {
    return res.status(400).json({ error: "Missing video URL" });
  }

  // 使用 yt-dlp 提取下载 URL
  const command = `yt-dlp --get-url --print-traffic "${videoUrl}"`;
  console.log(command);
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error: ${error.message}`);
      return res.status(500).json({ error: "Failed to fetch download URL" });
    }
    if (stderr) {
      console.error(`Stderr: ${stderr}`);
    }

    const downloadUrl = stdout.trim(); // 提取下载链接
    console.log(downloadUrl);
    res.json({ downloadUrl });
  });
});

// 获取上传者所有视频下载链接
app.post("/list-videos", (req, res) => {
  const videoUrl = req.body.url; // 客户端发送的 TikTok 视频 URL
  console.log(videoUrl);
  // 提取上传者名字
  function extractUploaderName(url) {
    const match = url.match(/https:\/\/www\.tiktok\.com\/@([^\/\?]+)/);
    if (match && match[1]) {
      return match[1]; // 提取到的上传者名字
    }
    return null; // 无法匹配时返回 null
  }
  const uploaderName = extractUploaderName(videoUrl);
  console.log(uploaderName);
  const uploaderUrl = `https://www.tiktok.com/@${uploaderName}`;

  // 构造 yt-dlp 命令
  const command = `yt-dlp --flat-playlist --playlist-items 1-10 -J "${uploaderUrl}"`;
  //   const command = `yt-dlp --flat-playlist --skip-download --print "{id:%(id)s,title:%(title)s,thumbnails:%(thumbnails_table)s,url:%(url)s}" -J "https://www.tiktok.com/@ddratpotato"`;

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error: ${error.message}`);
      return res.status(500).json({ error: "Failed to fetch video list" });
    }
    if (stderr) {
      console.error(`Stderr: ${stderr}`);
    }

    try {
      console.log(stdout);
      //   // 解析 JSON 输出
      const data = JSON.parse(stdout);

      //   // 提取视频信息
      //   const videoLinks = data.entries.map((entry) => {
      //     return {
      //       id: entry.id,
      //       title: entry.title,
      //       url: `https://www.tiktok.com/@${entry.id}`,
      //     };
      //   });

      // 返回结果
      res.json(data);
    } catch (parseError) {
      console.error(`JSON Parse Error: ${parseError.message}`);
      res.status(500).json({ error: "Failed to parse video list" });
    }
  });
});

const tiktok = require(".");

app.post("/get-url", (req, res) => {
  console.log("get-url");
  const videoUrl = req.body.url; // 客户端发送的 TikTok 视频 URL
  console.log(videoUrl);
  if (!videoUrl) {
    return res.status(400).json({ error: "Missing video URL" });
  }

  tiktok
    .tiktokdownload(videoUrl)
    .then((result) => {
      console.log(result);
      res.json(result);
    })
    .catch((e) => console.log(e));
});

// 启动 Express 服务器
const ip = "0.0.0.0";
const port = 3000;
app.listen(port, ip, () => {
  console.log(`Server running at http://${ip}:${port}`);
});
