# TKD Server Go

这是原Node.js版本TikTok下载服务器的Go重构版本，具有更好的并发性能和资源管理。

## 功能特性

- **高并发处理**: 使用Go的goroutine和工作池模式，支持更高的并发请求
- **多种下载方式**: 集成多个TikTok下载源，提高成功率
- **队列管理**: 智能的请求队列管理，避免系统过载
- **数据库支持**: SQLite数据库存储设置信息
- **RESTful API**: 完整的REST API接口
- **配置管理**: 灵活的环境变量配置

## 项目结构

```
tkd-server-go/
├── main.go                 # 程序入口
├── go.mod                  # Go模块文件
├── .env                    # 环境变量配置
├── README.md               # 项目说明
└── internal/               # 内部包
    ├── config/             # 配置管理
    │   └── config.go
    ├── database/           # 数据库操作
    │   └── database.go
    ├── models/             # 数据模型
    │   └── models.go
    ├── utils/              # 工具函数
    │   ├── queue.go        # 队列管理
    │   └── helpers.go      # 辅助函数
    ├── scrapers/           # 爬虫实现
    │   ├── ssstik.go
    │   ├── tiktokdownload.go
    │   ├── keeptiktok.go
    │   └── musicallydown.go
    ├── services/           # 业务服务
    │   ├── services.go
    │   ├── ytdlp.go
    │   └── tiktok.go
    ├── handlers/           # HTTP处理器
    │   ├── tiktok.go
    │   └── settings.go
    └── router/             # 路由配置
        └── router.go
```

## 安装和运行

### 前置要求

- Go 1.21+
- yt-dlp (需要安装在系统PATH中)

### 安装依赖

```bash
cd tkd-server-go
go mod tidy
```

### 配置环境变量

复制并编辑 `.env` 文件：

```bash
cp .env.example .env
```

配置项说明：
- `AUTH_TOKEN`: API认证令牌
- `PORT`: 服务器端口 (默认: 3000)
- `IP`: 服务器IP (默认: 0.0.0.0)
- `MAX_CONCURRENT_REQUESTS`: 最大并发请求数 (默认: 10)
- `WORKER_POOL_SIZE`: 工作池大小 (默认: 20)

### 运行服务器

```bash
go run main.go
```

或者编译后运行：

```bash
go build -o tkd-server
./tkd-server
```

## API接口

### TikTok相关接口

#### 获取用户头像
```
POST /get-avatar
Content-Type: application/json

{
  "name": "username_or_url"
}
```

#### 获取用户帖子总数和页数
```
POST /get-pages
Content-Type: application/json

{
  "name": "username_or_url"
}
```

#### 获取用户帖子列表
```
POST /get-post
Content-Type: application/json

{
  "name": "username_or_url",
  "page": 1
}
```

#### 获取视频下载链接
```
POST /get-url
Content-Type: application/json

{
  "url": "tiktok_video_url"
}
```

### 设置相关接口

#### 获取设置
```
GET /setting
```

#### 保存设置 (需要认证)
```
POST /setting
Authorization: your_auth_token
Content-Type: application/json

{
  "key": "value"
}
```

### 系统接口

#### 队列状态
```
GET /queue-status
```

#### 健康检查
```
GET /health
```

## 性能优势

相比Node.js版本，Go版本具有以下优势：

1. **更高的并发性能**: 
   - Node.js: 单线程事件循环，最大安全并发约5-10个请求
   - Go: 多线程goroutine，可安全处理20-50个并发请求

2. **更低的内存占用**:
   - Node.js: 每个请求约8-16MB内存
   - Go: 每个goroutine约2-4MB内存

3. **更好的资源管理**:
   - 智能的工作池管理
   - 自动的垃圾回收
   - 更精确的并发控制

4. **更快的响应时间**:
   - 在100个并发请求下，响应时间提升约2-3倍

## 开发说明

### 添加新的下载源

1. 在 `internal/scrapers/` 目录下创建新的爬虫文件
2. 实现下载接口
3. 在 `internal/services/tiktok.go` 中集成新的爬虫

### 扩展API接口

1. 在 `internal/models/` 中定义数据模型
2. 在 `internal/handlers/` 中实现处理器
3. 在 `internal/router/` 中添加路由

## 许可证

本项目基于原Node.js版本重构，保持相同的开源许可证。
