version: '3.8'

services:
  tkd-server-go:
    build: .
    ports:
      - "3000:3000"
    environment:
      - AUTH_TOKEN=jtD2EWgRu1tbij
      - PORT=3000
      - IP=0.0.0.0
      - NODE_ENV=production
      - MAX_CONCURRENT_REQUESTS=20
      - WORKER_POOL_SIZE=50
    volumes:
      - ./data:/root/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
