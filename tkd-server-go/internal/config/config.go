package config

import (
	"os"
	"strconv"
)

// Config 应用配置结构
type Config struct {
	// 服务器配置
	IP          string
	Port        string
	Environment string

	// 认证配置
	AuthToken string

	// 并发配置
	MaxConcurrentRequests int
	WorkerPoolSize        int
}

// Load 加载配置
func Load() *Config {
	return &Config{
		IP:                    getEnv("IP", "0.0.0.0"),
		Port:                  getEnv("PORT", "3000"),
		Environment:           getEnv("NODE_ENV", "development"),
		AuthToken:             getEnv("AUTH_TOKEN", ""),
		MaxConcurrentRequests: getEnvAsInt("MAX_CONCURRENT_REQUESTS", 10),
		WorkerPoolSize:        getEnvAsInt("WORKER_POOL_SIZE", 20),
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数，如果不存在或转换失败则返回默认值
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
