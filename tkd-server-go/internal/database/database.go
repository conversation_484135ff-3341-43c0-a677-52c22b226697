package database

import (
	"database/sql"
	"encoding/json"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

// DB 数据库连接
type DB struct {
	*sql.DB
}

// Init 初始化数据库
func Init() (*DB, error) {
	// 创建数据库文件路径
	dbPath := filepath.Join(".", "settings.db")
	
	// 打开数据库连接
	sqlDB, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	db := &DB{sqlDB}

	// 创建表
	if err := db.createTables(); err != nil {
		return nil, err
	}

	return db, nil
}

// createTables 创建数据库表
func (db *DB) createTables() error {
	query := `
	CREATE TABLE IF NOT EXISTS settings (
		id INTEGER PRIMARY KEY DEFAULT 1,
		data TEXT NOT NULL,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := db.Exec(query)
	return err
}

// GetSettings 获取设置
func (db *DB) GetSettings() (map[string]interface{}, error) {
	var data string
	query := "SELECT data FROM settings WHERE id = 1"
	
	err := db.QueryRow(query).Scan(&data)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 没有设置数据
		}
		return nil, err
	}

	var settings map[string]interface{}
	if err := json.Unmarshal([]byte(data), &settings); err != nil {
		return nil, err
	}

	return settings, nil
}

// SaveSettings 保存设置
func (db *DB) SaveSettings(settings map[string]interface{}) error {
	data, err := json.Marshal(settings)
	if err != nil {
		return err
	}

	// 检查是否已存在记录
	var exists bool
	checkQuery := "SELECT EXISTS(SELECT 1 FROM settings WHERE id = 1)"
	err = db.QueryRow(checkQuery).Scan(&exists)
	if err != nil {
		return err
	}

	if exists {
		// 更新现有记录
		updateQuery := "UPDATE settings SET data = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1"
		_, err = db.Exec(updateQuery, string(data))
	} else {
		// 插入新记录
		insertQuery := "INSERT INTO settings (id, data) VALUES (1, ?)"
		_, err = db.Exec(insertQuery, string(data))
	}

	return err
}
