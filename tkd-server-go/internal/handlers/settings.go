package handlers

import (
	"net/http"
	"tkd-server-go/internal/models"
	"tkd-server-go/internal/services"

	"github.com/gin-gonic/gin"
)

// SettingsHandler 设置处理器
type SettingsHandler struct {
	services *services.Services
}

// NewSettingsHandler 创建新的设置处理器
func NewSettingsHandler(services *services.Services) *SettingsHandler {
	return &SettingsHandler{
		services: services,
	}
}

// GetSettings 获取设置
func (h *SettingsHandler) GetSettings(c *gin.Context) {
	settings, err := h.services.DB.GetSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to get settings",
			Details: err.Error(),
		})
		return
	}

	if settings == nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "No settings found",
		})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, settings)
}

// SaveSettings 保存设置（需要验证）
func (h *SettingsHandler) SaveSettings(c *gin.Context) {
	// 检查认证头
	authToken := c.GetHeader("Authorization")
	if authToken == "" {
		authToken = c.GetHeader("auth-token")
	}

	if !h.verifyAuthToken(authToken) {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Unauthorized",
		})
		return
	}

	// 解析请求体
	var settings map[string]interface{}
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request body",
			Details: err.Error(),
		})
		return
	}

	// 保存设置
	if err := h.services.DB.SaveSettings(settings); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to save settings",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Settings saved successfully",
	})
}

// verifyAuthToken 验证认证token
func (h *SettingsHandler) verifyAuthToken(token string) bool {
	if token == "" || h.services.Config.AuthToken == "" {
		return false
	}

	// 直接比对token
	return token == h.services.Config.AuthToken
}
