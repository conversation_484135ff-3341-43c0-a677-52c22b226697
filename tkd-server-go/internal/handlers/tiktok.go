package handlers

import (
	"net/http"
	"strconv"
	"tkd-server-go/internal/models"
	"tkd-server-go/internal/services"
	"tkd-server-go/internal/utils"

	"github.com/gin-gonic/gin"
)

// TiktokHandler TikTok处理器
type TiktokHandler struct {
	services *services.Services
}

// NewTiktokHandler 创建新的TikTok处理器
func NewTiktokHandler(services *services.Services) *TiktokHandler {
	return &TiktokHandler{
		services: services,
	}
}

// GetAvatar 获取用户头像
func (h *TiktokHandler) GetAvatar(c *gin.Context) {
	var req models.RequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing name",
		})
		return
	}

	if req.Name == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing name",
		})
		return
	}

	avatar, err := h.services.Tiktok.GetUserAvatar(req.Name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to fetch avatar",
			Details: err.Error(),
			Name:    req.Name,
		})
		return
	}

	if avatar == nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error: "Could not extract avatar URLs",
		})
		return
	}

	c.JSON(http.StatusOK, avatar)
}

// GetPages 获取用户的总帖子数和总页数
func (h *TiktokHandler) GetPages(c *gin.Context) {
	var req models.RequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing name",
		})
		return
	}

	if req.Name == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing name",
		})
		return
	}

	username := utils.ExtractUsernameFromNameOrURL(req.Name)

	totalPosts, err := h.services.Ytdlp.GetTotalPosts(username)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to fetch total posts count",
			Details: err.Error(),
		})
		return
	}

	itemsPerPage := 9
	totalPages := utils.CalculateTotalPages(totalPosts, itemsPerPage)

	pageInfo := models.PageInfo{
		Name:         username,
		TotalPosts:   totalPosts,
		TotalPages:   totalPages,
		ItemsPerPage: itemsPerPage,
	}

	c.JSON(http.StatusOK, pageInfo)
}

// GetPost 获取用户帖子列表
func (h *TiktokHandler) GetPost(c *gin.Context) {
	var req models.RequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing name",
		})
		return
	}

	if req.Name == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing name",
		})
		return
	}

	page := req.Page
	if page <= 0 {
		page = 1
	}

	username := utils.ExtractUsernameFromNameOrURL(req.Name)

	posts, err := h.services.Ytdlp.GetUserPosts(username, page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to fetch data using yt-dlp",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, posts)
}

// GetURL 获取视频下载链接
func (h *TiktokHandler) GetURL(c *gin.Context) {
	var req models.RequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing video URL",
		})
		return
	}

	if req.URL == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Missing video URL",
		})
		return
	}

	videoLinks, err := h.services.Tiktok.DownloadVideo(req.URL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "All methods failed",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, videoLinks)
}

// GetQueueStatus 获取队列状态
func (h *TiktokHandler) GetQueueStatus(c *gin.Context) {
	status := h.services.Ytdlp.GetStatus()
	c.JSON(http.StatusOK, status)
}
