package models

// TikTokPost TikTok帖子结构
type TikTokPost struct {
	ID        string `json:"id"`
	Desc      string `json:"desc"`
	Cover     string `json:"cover"`
	URL       string `json:"url"`
	UserID    string `json:"userid"`
	Name      string `json:"name"`
	Avatar    string `json:"avatar"`
	Timestamp int64  `json:"timestamp"`
	TimesRead string `json:"timesread"`
	Page      int    `json:"page,omitempty"`
}

// TikTokAvatar TikTok用户头像结构
type TikTokAvatar struct {
	Large  string `json:"large"`
	Medium string `json:"medium"`
	Thumb  string `json:"thumb"`
}

// PageInfo 分页信息结构
type PageInfo struct {
	Name         string `json:"name"`
	TotalPosts   int    `json:"totalPosts"`
	TotalPages   int    `json:"totalPages"`
	ItemsPerPage int    `json:"itemsPerPage"`
}

// VideoLinks 视频下载链接结构
type VideoLinks struct {
	Links []string `json:"links"`
}

// DownloadResult 下载结果结构
type DownloadResult struct {
	Status      bool   `json:"status"`
	Text        string `json:"text,omitempty"`
	VideoNoWM   string `json:"videonowm,omitempty"`
	VideoNoWM2  string `json:"videonowm2,omitempty"`
	Music       string `json:"music,omitempty"`
	NoWM        string `json:"nowm,omitempty"`
	WM          string `json:"wm,omitempty"`
	Audio       string `json:"audio,omitempty"`
	Message     string `json:"message,omitempty"`
	Error       string `json:"e,omitempty"`
}

// RequestBody 通用请求体结构
type RequestBody struct {
	Name string `json:"name"`
	URL  string `json:"url"`
	Page int    `json:"page"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Error   string `json:"error"`
	Details string `json:"details,omitempty"`
	Name    string `json:"name,omitempty"`
}

// SuccessResponse 成功响应结构
type SuccessResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// QueueStatus 队列状态结构
type QueueStatus struct {
	QueueLength   int `json:"queueLength"`
	CurrentRunning int `json:"currentRunning"`
	MaxConcurrent int `json:"maxConcurrent"`
}
