package router

import (
	"tkd-server-go/internal/config"
	"tkd-server-go/internal/handlers"
	"tkd-server-go/internal/services"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(services *services.Services, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 设置CORS
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization", "auth-token"}
	r.Use(cors.New(corsConfig))

	// 创建处理器
	tiktokHandler := handlers.NewTiktokHandler(services)
	settingsHandler := handlers.NewSettingsHandler(services)

	// TikTok相关路由
	r.POST("/get-avatar", tiktokHandler.GetAvatar)
	r.POST("/get-pages", tiktokHandler.GetPages)
	r.POST("/get-post", tiktokHandler.GetPost)
	r.POST("/get-url", tiktokHandler.GetURL)

	// 设置相关路由
	r.GET("/setting", settingsHandler.GetSettings)
	r.POST("/setting", settingsHandler.SaveSettings)

	// 队列状态路由
	r.GET("/queue-status", tiktokHandler.GetQueueStatus)

	// 健康检查路由
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"service": "tkd-server-go",
		})
	})

	return r
}
