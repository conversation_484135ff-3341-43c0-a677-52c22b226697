package scrapers

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"tkd-server-go/internal/models"

	"github.com/PuerkitoBio/goquery"
)

// KeepTiktokScraper keeptiktok.com 爬虫
type KeepTiktokScraper struct {
	baseURL string
	client  *http.Client
}

// NewKeepTiktokScraper 创建新的 keeptiktok 爬虫
func NewKeepTiktokScraper() *KeepTiktokScraper {
	return &KeepTiktokScraper{
		baseURL: "https://keeptiktok.com",
		client:  &http.Client{},
	}
}

// Download 下载视频
func (k *KeepTiktokScraper) Download(videoURL string) (*models.DownloadResult, error) {
	// 第一步：获取页面和token
	req, err := http.NewRequest("GET", k.baseURL+"/?lang=ID", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("<PERSON>ie", "__cfduid=d5db462e7efb9bb76bcf89765dbd896c91617891082; PHPSESSID=5a017bebc34ef170ddec3b7c71a0bbe8; _ga=GA1.2.1193000489.1617891094; _gid=GA1.2.408908588.1617891094; ads=ok; __atuvc=3|14; __atuvs=606f0f171d8ce8a1002; __atssc=google;2")

	resp, err := k.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 解析HTML获取token
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	token, exists := doc.Find("input#token").Attr("value")
	if !exists {
		return &models.DownloadResult{
			Status:  false,
			Message: "无法获取token",
		}, nil
	}

	// 第二步：创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加表单字段
	writer.WriteField("url", videoURL)
	writer.WriteField("token", token)
	writer.Close()

	postReq, err := http.NewRequest("POST", k.baseURL+"/index.php", &buf)
	if err != nil {
		return nil, err
	}

	// 设置POST请求头
	postReq.Header.Set("Content-Type", writer.FormDataContentType())
	postReq.Header.Set("Cookie", "__cfduid=d5db462e7efb9bb76bcf89765dbd896c91617891082; PHPSESSID=5a017bebc34ef170ddec3b7c71a0bbe8; _ga=GA1.2.1193000489.1617891094; _gid=GA1.2.408908588.1617891094; ads=ok; __atuvc=3|14; __atuvs=606f0f171d8ce8a1002; __atssc=google;2")

	postResp, err := k.client.Do(postReq)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "请求失败",
			Error:   err.Error(),
		}, nil
	}
	defer postResp.Body.Close()

	// 解析结果页面
	resultDoc, err := goquery.NewDocumentFromReader(postResp.Body)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "解析响应失败",
			Error:   err.Error(),
		}, nil
	}

	// 获取视频描述
	text := resultDoc.Find("div.download-info > div.video_des").Text()

	// 获取canonical链接作为referer
	canonicalLink, _ := resultDoc.Find(`link[rel="canonical"]`).Attr("href")

	// 第三步：下载视频文件
	dlReq, err := http.NewRequest("GET", k.baseURL+"/dl.php", nil)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "创建下载请求失败",
			Error:   err.Error(),
		}, nil
	}

	dlReq.Header.Set("Referer", canonicalLink)
	dlReq.Header.Set("Cookie", "__cfduid=d5db462e7efb9bb76bcf89765dbd896c91617891082; PHPSESSID=5a017bebc34ef170ddec3b7c71a0bbe8; _ga=GA1.2.1193000489.1617891094; _gid=GA1.2.408908588.1617891094; ads=ok; __atuvc=3|14; __atuvs=606f0f171d8ce8a1002; __atssc=google;2")

	dlResp, err := k.client.Do(dlReq)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "下载失败",
			Error:   err.Error(),
		}, nil
	}
	defer dlResp.Body.Close()

	// 读取视频数据并转换为base64
	videoData, err := io.ReadAll(dlResp.Body)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "读取视频数据失败",
			Error:   err.Error(),
		}, nil
	}

	base64Data := base64.StdEncoding.EncodeToString(videoData)

	result := &models.DownloadResult{
		Status: true,
		Text:   text,
		// 这里我们将base64数据放在VideoNoWM字段中，实际使用时可能需要调整
		VideoNoWM: fmt.Sprintf("data:video/mp4;base64,%s", base64Data),
	}

	return result, nil
}
