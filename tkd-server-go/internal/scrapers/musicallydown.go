package scrapers

import (
	"bytes"
	"mime/multipart"
	"net/http"
	"tkd-server-go/internal/models"

	"github.com/PuerkitoBio/goquery"
)

// MusicallyDownScraper musicallydown.com 爬虫
type MusicallyDownScraper struct {
	baseURL string
	client  *http.Client
}

// NewMusicallyDownScraper 创建新的 musicallydown 爬虫
func NewMusicallyDownScraper() *MusicallyDownScraper {
	return &MusicallyDownScraper{
		baseURL: "https://musicallydown.com",
		client:  &http.Client{},
	}
}

// Download 下载视频
func (m *MusicallyDownScraper) Download(videoURL string) (*models.DownloadResult, error) {
	// 第一步：获取页面和表单信息
	req, err := http.NewRequest("GET", m.baseURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36")
	req.Header.Set("Cookie", "__cfduid=d1a03c762459f64f87734977f474142fe1618464905; session_data=ac6a59adeffddbf12d71d4d9e368fee9; _ga=GA1.2.1692872429.1618464910; _gid=GA1.2.371863113.1618464910; __atuvc=2%7C15; __atuvs=6077d08d902cbf1a001; __atssc=google%3B2")

	resp, err := m.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 解析HTML获取表单信息
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	// 获取表单输入字段
	var keyInputs []struct {
		Name  string
		Value string
	}

	doc.Find("form > div > div > input").Each(func(i int, s *goquery.Selection) {
		name, nameExists := s.Attr("name")
		value, valueExists := s.Attr("value")
		if nameExists {
			keyInput := struct {
				Name  string
				Value string
			}{
				Name: name,
			}
			if valueExists {
				keyInput.Value = value
			}
			keyInputs = append(keyInputs, keyInput)
		}
	})

	if len(keyInputs) < 3 {
		return &models.DownloadResult{
			Status:  false,
			Message: "无法获取表单信息",
		}, nil
	}

	// 第二步：创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加表单字段
	writer.WriteField(keyInputs[0].Name, videoURL)
	writer.WriteField(keyInputs[1].Name, keyInputs[1].Value)
	writer.WriteField(keyInputs[2].Name, keyInputs[2].Value)
	writer.Close()

	postReq, err := http.NewRequest("POST", m.baseURL+"/download", &buf)
	if err != nil {
		return nil, err
	}

	// 设置POST请求头
	postReq.Header.Set("Content-Type", writer.FormDataContentType())
	postReq.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36")
	postReq.Header.Set("Cookie", "__cfduid=d1a03c762459f64f87734977f474142fe1618464905; session_data=ac6a59adeffddbf12d71d4d9e368fee9; _ga=GA1.2.1692872429.1618464910; _gid=GA1.2.371863113.1618464910; __atuvc=2%7C15; __atuvs=6077d08d902cbf1a001; __atssc=google%3B2")
	postReq.Header.Set("Origin", m.baseURL)
	postReq.Header.Set("Referer", m.baseURL+"/")

	postResp, err := m.client.Do(postReq)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "请求失败",
			Error:   err.Error(),
		}, nil
	}
	defer postResp.Body.Close()

	// 解析结果
	resultDoc, err := goquery.NewDocumentFromReader(postResp.Body)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "解析响应失败",
			Error:   err.Error(),
		}, nil
	}

	// 提取信息
	title := resultDoc.Find("div.row > div > h2 > b").Text()
	preview, _ := resultDoc.Find("div.row > div > h1.cushead.white-text > video#video").Attr("poster")
	
	// 获取下载链接
	downloadLink, _ := resultDoc.Find("div.row > div > a:nth-child(4)").Attr("href")
	downloadDirectLink, _ := resultDoc.Find("div.row > div > a:nth-child(5)").Attr("href")

	result := &models.DownloadResult{
		Status:    true,
		Message:   "Created By MRHRTZ",
		Text:      title,
		VideoNoWM: downloadLink,
		VideoNoWM2: downloadDirectLink,
		// 这里可以添加preview字段如果需要的话
	}

	return result, nil
}
