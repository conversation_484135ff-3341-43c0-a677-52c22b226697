package scrapers

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"tkd-server-go/internal/models"

	"github.com/PuerkitoBio/goquery"
)

// SsstikScraper ssstik.io 爬虫
type SsstikScraper struct {
	baseURL string
	client  *http.Client
}

// NewSsstikScraper 创建新的 ssstik 爬虫
func NewSsstikScraper() *SsstikScraper {
	return &SsstikScraper{
		baseURL: "https://ssstik.io",
		client:  &http.Client{},
	}
}

// Download 下载视频
func (s *SsstikScraper) Download(videoURL string) (*models.DownloadResult, error) {
	// 第一步：获取页面和token
	req, err := http.NewRequest("GET", s.baseURL, nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36")
	req.Header.Set("Cookie", "__cfduid=deb9cec7a40793d1abe009bb9961a92d41617497572; PHPSESSID=7ivsp9hc6askg1qocpi8lfpn7n; __cflb=02DiuEcwseaiqqyPC5q2cQqNGembhyZ5QaychuqFzev83; _ga=GA1.2.131585469.1617497575; _gid=GA1.2.1629908100.1617497575; _gat_UA-3524196-6=1")
	req.Header.Set("sec-ch-ua", `"Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"`)

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 解析HTML获取token
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	// 获取表单信息
	form := doc.Find(`form[hx-target="#target"]`)
	urlPost, exists := form.Attr("hx-post")
	if !exists {
		return nil, fmt.Errorf("无法找到提交URL")
	}

	tokenJSON, exists := form.Attr("include-vals")
	if !exists {
		return nil, fmt.Errorf("无法找到token信息")
	}

	// 解析token
	tokenJSON = strings.ReplaceAll(tokenJSON, "'", "")
	parts := strings.Split(tokenJSON, ",")
	var tt, ts string
	
	for _, part := range parts {
		if strings.HasPrefix(part, "tt:") {
			tt = strings.TrimPrefix(part, "tt:")
		} else if strings.HasPrefix(part, "ts:") {
			ts = strings.TrimPrefix(part, "ts:")
		}
	}

	// 第二步：提交表单
	formData := url.Values{}
	formData.Set("id", videoURL)
	formData.Set("locale", "en")
	formData.Set("tt", tt)
	formData.Set("ts", ts)

	postReq, err := http.NewRequest("POST", s.baseURL+urlPost, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, err
	}

	// 设置POST请求头
	postReq.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	postReq.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36")
	postReq.Header.Set("Cookie", "__cfduid=deb9cec7a40793d1abe009bb9961a92d41617497572; PHPSESSID=7ivsp9hc6askg1qocpi8lfpn7n; __cflb=02DiuEcwseaiqqyPC5q2cQqNGembhyZ5QaychuqFzev83; _ga=GA1.2.131585469.1617497575; _gid=GA1.2.1629908100.1617497575; _gat_UA-3524196-6=1")
	postReq.Header.Set("sec-ch-ua", `"Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"`)

	postResp, err := s.client.Do(postReq)
	if err != nil {
		return nil, err
	}
	defer postResp.Body.Close()

	// 解析结果
	resultDoc, err := goquery.NewDocumentFromReader(postResp.Body)
	if err != nil {
		return nil, err
	}

	// 提取下载链接
	text := resultDoc.Find("div > p").Text()
	videoNoWM, _ := resultDoc.Find("div > a.without_watermark").Attr("href")
	videoNoWM2, _ := resultDoc.Find("div > a.without_watermark_direct").Attr("href")
	music, _ := resultDoc.Find("div > a.music").Attr("href")

	// 构建完整URL
	if videoNoWM != "" && !strings.HasPrefix(videoNoWM, "http") {
		videoNoWM = s.baseURL + videoNoWM
	}

	result := &models.DownloadResult{
		Status:     true,
		Text:       text,
		VideoNoWM:  videoNoWM,
		VideoNoWM2: videoNoWM2,
		Music:      music,
	}

	// 检查是否成功
	if videoNoWM2 == "" {
		result.Status = false
		result.Message = "Tautan ini telah terunduh sebelumnya"
		return result, fmt.Errorf("下载失败")
	}

	return result, nil
}
