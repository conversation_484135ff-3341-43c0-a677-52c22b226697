package scrapers

import (
	"net/http"
	"net/url"
	"strings"
	"tkd-server-go/internal/models"

	"github.com/PuerkitoBio/goquery"
)

// TiktokDownloadScraper ttdownloader.com 爬虫
type TiktokDownloadScraper struct {
	baseURL string
	client  *http.Client
}

// NewTiktokDownloadScraper 创建新的 ttdownloader 爬虫
func NewTiktokDownloadScraper() *TiktokDownloadScraper {
	return &TiktokDownloadScraper{
		baseURL: "https://ttdownloader.com",
		client:  &http.Client{},
	}
}

// Download 下载视频
func (t *TiktokDownloadScraper) Download(videoURL string) (*models.DownloadResult, error) {
	// 第一步：获取页面和token
	req, err := http.NewRequest("GET", t.baseURL, nil)
	if err != nil {
		return nil, err
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 解析HTML获取token和cookie
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	token, exists := doc.Find("#token").Attr("value")
	if !exists {
		return &models.DownloadResult{
			Status:  false,
			Message: "无法获取token",
		}, nil
	}

	// 获取cookie
	var cookies []string
	for _, cookie := range resp.Cookies() {
		cookies = append(cookies, cookie.String())
	}
	cookieStr := strings.Join(cookies, "; ")

	// 第二步：提交表单
	formData := url.Values{}
	formData.Set("url", videoURL)
	formData.Set("format", "")
	formData.Set("token", token)

	postReq, err := http.NewRequest("POST", t.baseURL+"/search/", strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, err
	}

	// 设置POST请求头
	postReq.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	postReq.Header.Set("Origin", t.baseURL)
	postReq.Header.Set("Referer", t.baseURL+"/")
	postReq.Header.Set("Cookie", cookieStr)

	postResp, err := t.client.Do(postReq)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "请求失败",
			Error:   err.Error(),
		}, nil
	}
	defer postResp.Body.Close()

	// 解析结果
	resultDoc, err := goquery.NewDocumentFromReader(postResp.Body)
	if err != nil {
		return &models.DownloadResult{
			Status:  false,
			Message: "解析响应失败",
			Error:   err.Error(),
		}, nil
	}

	// 提取下载链接
	noWM, _ := resultDoc.Find("#results-list > div:nth-child(2) > div.download > a").Attr("href")
	wm, _ := resultDoc.Find("#results-list > div:nth-child(3) > div.download > a").Attr("href")
	audio, _ := resultDoc.Find("#results-list > div:nth-child(4) > div.download > a").Attr("href")

	result := &models.DownloadResult{
		Status: true,
		NoWM:   noWM,
		WM:     wm,
		Audio:  audio,
	}

	return result, nil
}
