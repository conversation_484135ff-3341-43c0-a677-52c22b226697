package services

import (
	"tkd-server-go/internal/config"
	"tkd-server-go/internal/database"
)

// Services 服务集合
type Services struct {
	DB      *database.DB
	Config  *config.Config
	Ytdlp   *YtdlpService
	Tiktok  *TiktokService
}

// NewServices 创建新的服务集合
func NewServices(db *database.DB, cfg *config.Config) *Services {
	return &Services{
		DB:     db,
		Config: cfg,
		Ytdlp:  NewYtdlpService(cfg.WorkerPoolSize),
		Tiktok: NewTiktokService(),
	}
}

// Close 关闭所有服务
func (s *Services) Close() {
	if s.Ytdlp != nil {
		s.Ytdlp.Close()
	}
}
