package services

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"tkd-server-go/internal/models"
	"tkd-server-go/internal/scrapers"
	"tkd-server-go/internal/utils"

	"github.com/PuerkitoBio/goquery"
)

// TiktokService TikTok服务
type TiktokService struct {
	client             *http.Client
	ssstikScraper      *scrapers.SsstikScraper
	tiktokScraper      *scrapers.TiktokDownloadScraper
	keepTiktokScraper  *scrapers.KeepTiktokScraper
	musicallyDownScraper *scrapers.MusicallyDownScraper
}

// NewTiktokService 创建新的TikTok服务
func NewTiktokService() *TiktokService {
	return &TiktokService{
		client:             &http.Client{},
		ssstikScraper:      scrapers.NewSsstikScraper(),
		tiktokScraper:      scrapers.NewTiktokDownloadScraper(),
		keepTiktokScraper:  scrapers.NewKeepTiktokScraper(),
		musicallyDownScraper: scrapers.NewMusicallyDownScraper(),
	}
}

// GetUserAvatar 获取用户头像
func (t *TiktokService) GetUserAvatar(name string) (*models.TikTokAvatar, error) {
	username := utils.ExtractUsernameFromNameOrURL(name)
	url := utils.BuildTikTokUserURL(username)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 解析HTML内容
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	// 提取TikTok用户头像
	avatar, err := t.extractTikTokAvatar(doc.Text())
	if err != nil {
		return nil, err
	}

	return avatar, nil
}

// extractTikTokAvatar 从HTML内容中提取TikTok用户头像
func (t *TiktokService) extractTikTokAvatar(htmlContent string) (*models.TikTokAvatar, error) {
	// 查找包含用户信息的JSON数据
	scriptTagRegex := regexp.MustCompile(`<script id="__UNIVERSAL_DATA_FOR_REHYDRATION__" type="application/json">(.*?)</script>`)
	scriptMatch := scriptTagRegex.FindStringSubmatch(htmlContent)
	
	if len(scriptMatch) < 2 {
		return nil, fmt.Errorf("无法找到用户数据JSON")
	}

	// 这里需要解析JSON数据，但由于Go的JSON解析比较严格，
	// 我们简化处理，使用正则表达式提取头像URL
	content := scriptMatch[1]
	
	// 提取头像URL的正则表达式
	avatarRegex := regexp.MustCompile(`"avatarLarger":"([^"]+)".*?"avatarMedium":"([^"]+)".*?"avatarThumb":"([^"]+)"`)
	avatarMatch := avatarRegex.FindStringSubmatch(content)
	
	if len(avatarMatch) < 4 {
		return nil, fmt.Errorf("无法找到头像信息")
	}

	// 解码URL中的转义字符
	large := strings.ReplaceAll(avatarMatch[1], "\\u002F", "/")
	medium := strings.ReplaceAll(avatarMatch[2], "\\u002F", "/")
	thumb := strings.ReplaceAll(avatarMatch[3], "\\u002F", "/")

	return &models.TikTokAvatar{
		Large:  large,
		Medium: medium,
		Thumb:  thumb,
	}, nil
}

// DownloadVideo 下载视频，尝试多种方法
func (t *TiktokService) DownloadVideo(videoURL string) (*models.VideoLinks, error) {
	// 方法1：尝试ssstik
	if result, err := t.ssstikScraper.Download(videoURL); err == nil && result.Status {
		links := []string{}
		if result.VideoNoWM2 != "" {
			links = append(links, result.VideoNoWM2)
		}
		if result.VideoNoWM != "" {
			links = append(links, result.VideoNoWM)
		}
		if len(links) > 0 {
			return &models.VideoLinks{Links: links}, nil
		}
	}

	// 方法2：尝试ttdownloader
	if result, err := t.tiktokScraper.Download(videoURL); err == nil && result.Status {
		links := []string{}
		if result.NoWM != "" {
			links = append(links, result.NoWM)
		}
		if result.WM != "" {
			links = append(links, result.WM)
		}
		if len(links) > 0 {
			return &models.VideoLinks{Links: links}, nil
		}
	}

	// 方法3：尝试keeptiktok
	if result, err := t.keepTiktokScraper.Download(videoURL); err == nil && result.Status {
		links := []string{}
		if result.VideoNoWM != "" {
			links = append(links, result.VideoNoWM)
		}
		if len(links) > 0 {
			return &models.VideoLinks{Links: links}, nil
		}
	}

	// 方法4：尝试musicallydown
	if result, err := t.musicallyDownScraper.Download(videoURL); err == nil && result.Status {
		links := []string{}
		if result.VideoNoWM2 != "" {
			links = append(links, result.VideoNoWM2)
		}
		if result.VideoNoWM != "" {
			links = append(links, result.VideoNoWM)
		}
		if len(links) > 0 {
			return &models.VideoLinks{Links: links}, nil
		}
	}

	return nil, fmt.Errorf("所有下载方法都失败了")
}
