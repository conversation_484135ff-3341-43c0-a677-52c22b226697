package services

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"
	"tkd-server-go/internal/models"
	"tkd-server-go/internal/utils"
)

// YtdlpService yt-dlp服务
type YtdlpService struct {
	workerPool *utils.WorkerPool
}

// NewYtdlpService 创建新的yt-dlp服务
func NewYtdlpService(workerPoolSize int) *YtdlpService {
	return &YtdlpService{
		workerPool: utils.NewWorkerPool(workerPoolSize),
	}
}

// GetTotalPosts 获取用户总帖子数
func (y *YtdlpService) GetTotalPosts(username string) (int, error) {
	url := utils.BuildTikTokUserURL(username)
	command := fmt.Sprintf("yt-dlp --flat-playlist --skip-download --print playlist_count %s", url)

	job := utils.JobFunc(func(ctx context.Context) (interface{}, error) {
		return y.executeCommand(ctx, command)
	})

	resultChan := y.workerPool.Submit(job)
	
	select {
	case result := <-resultChan:
		if result.Error != nil {
			return 0, result.Error
		}
		
		output := result.Result.(string)
		lines := strings.Split(strings.TrimSpace(output), "\n")
		if len(lines) > 0 {
			if count, err := strconv.Atoi(strings.TrimSpace(lines[0])); err == nil {
				return count, nil
			}
		}
		return 0, nil
	case <-time.After(30 * time.Second):
		return 0, fmt.Errorf("获取总帖子数超时")
	}
}

// GetUserPosts 获取用户帖子列表
func (y *YtdlpService) GetUserPosts(username string, page int) ([]*models.TikTokPost, error) {
	itemsPerPage := 9
	startItem, endItem := utils.CalculatePagination(page, itemsPerPage)
	playlistItems := fmt.Sprintf("%d-%d", startItem, endItem)
	
	url := utils.BuildTikTokUserURL(username)
	command := fmt.Sprintf("yt-dlp --flat-playlist --playlist-items %s --dump-json %s", playlistItems, url)

	job := utils.JobFunc(func(ctx context.Context) (interface{}, error) {
		return y.executeCommand(ctx, command)
	})

	resultChan := y.workerPool.Submit(job)
	
	select {
	case result := <-resultChan:
		if result.Error != nil {
			return nil, result.Error
		}
		
		output := result.Result.(string)
		return y.parsePostsFromOutput(output, username, page)
	case <-time.After(60 * time.Second):
		return nil, fmt.Errorf("获取用户帖子超时")
	}
}

// executeCommand 执行命令
func (y *YtdlpService) executeCommand(ctx context.Context, command string) (string, error) {
	cmd := exec.CommandContext(ctx, "sh", "-c", command)
	
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行命令失败: %v", err)
	}
	
	return string(output), nil
}

// parsePostsFromOutput 从yt-dlp输出解析帖子
func (y *YtdlpService) parsePostsFromOutput(output, username string, page int) ([]*models.TikTokPost, error) {
	lines := strings.Split(strings.TrimSpace(output), "\n")
	
	// 如果没有输出，返回空数组
	if len(lines) == 0 || lines[0] == "" {
		return []*models.TikTokPost{}, nil
	}

	var posts []*models.TikTokPost
	
	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		
		var rawPost map[string]interface{}
		if err := json.Unmarshal([]byte(line), &rawPost); err != nil {
			continue // 跳过无法解析的行
		}
		
		post := y.convertRawPostToModel(rawPost, username, page)
		if post != nil {
			posts = append(posts, post)
		}
	}
	
	// 按时间戳倒序排序
	for i := 0; i < len(posts)-1; i++ {
		for j := i + 1; j < len(posts); j++ {
			if posts[i].Timestamp < posts[j].Timestamp {
				posts[i], posts[j] = posts[j], posts[i]
			}
		}
	}
	
	return posts, nil
}

// convertRawPostToModel 将原始数据转换为模型
func (y *YtdlpService) convertRawPostToModel(rawPost map[string]interface{}, username string, page int) *models.TikTokPost {
	post := &models.TikTokPost{
		Page: page,
	}
	
	// 提取ID
	if id, ok := rawPost["id"].(string); ok {
		post.ID = id
	}
	
	// 提取描述
	if title, ok := rawPost["title"].(string); ok {
		post.Desc = title
	} else if desc, ok := rawPost["description"].(string); ok {
		post.Desc = desc
	}
	
	// 提取缩略图
	if thumbnails, ok := rawPost["thumbnails"].([]interface{}); ok && len(thumbnails) > 0 {
		if thumbnail, ok := thumbnails[0].(map[string]interface{}); ok {
			if url, ok := thumbnail["url"].(string); ok {
				post.Cover = url
			}
		}
	}
	
	// 构建URL
	if post.ID != "" {
		post.URL = utils.BuildTikTokVideoURL(username, post.ID)
	} else if webpageURL, ok := rawPost["webpage_url"].(string); ok {
		post.URL = webpageURL
	}
	
	// 提取上传者信息
	if uploaderID, ok := rawPost["uploader_id"].(string); ok {
		post.UserID = uploaderID
	}
	
	if uploader, ok := rawPost["uploader"].(string); ok {
		post.Name = uploader
	} else {
		post.Name = username
	}
	
	// 提取时间戳
	if timestamp, ok := rawPost["timestamp"].(float64); ok {
		post.Timestamp = int64(timestamp)
		post.TimesRead = time.Unix(int64(timestamp), 0).Format("2006-01-02 15:04:05")
	} else {
		post.Timestamp = time.Now().Unix()
		post.TimesRead = time.Now().Format("2006-01-02 15:04:05")
	}
	
	return post
}

// GetStatus 获取工作池状态
func (y *YtdlpService) GetStatus() models.QueueStatus {
	queueLength, currentJobs, maxWorkers := y.workerPool.GetStatus()
	return models.QueueStatus{
		QueueLength:    queueLength,
		CurrentRunning: currentJobs,
		MaxConcurrent:  maxWorkers,
	}
}

// Close 关闭服务
func (y *YtdlpService) Close() {
	if y.workerPool != nil {
		y.workerPool.Close()
	}
}
