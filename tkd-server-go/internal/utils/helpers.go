package utils

import (
	"regexp"
	"strings"
)

// ExtractUploaderName 从TikTok URL中提取上传者名字
func ExtractUploaderName(url string) string {
	re := regexp.MustCompile(`https://www\.tiktok\.com/@([^/\?]+)`)
	matches := re.FindStringSubmatch(url)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// ExtractUsernameFromNameOrURL 从用户名或URL中提取用户名
func ExtractUsernameFromNameOrURL(name string) string {
	if strings.Contains(name, "tiktok.com/@") {
		re := regexp.MustCompile(`tiktok\.com/@([^/\?]+)`)
		matches := re.FindStringSubmatch(name)
		if len(matches) > 1 {
			return matches[1]
		}
	}
	return name
}

// BuildTikTokUserURL 构建TikTok用户URL
func BuildTikTokUserURL(username string) string {
	return "https://www.tiktok.com/@" + username
}

// BuildTikTokVideoURL 构建TikTok视频URL
func BuildTikTokVideoURL(username, videoID string) string {
	return "https://www.tiktok.com/@" + username + "/video/" + videoID
}

// CalculatePagination 计算分页信息
func CalculatePagination(page, itemsPerPage int) (startItem, endItem int) {
	startItem = (page-1)*itemsPerPage + 1
	endItem = page * itemsPerPage
	return
}

// CalculateTotalPages 计算总页数
func CalculateTotalPages(totalItems, itemsPerPage int) int {
	if totalItems == 0 {
		return 0
	}
	return (totalItems + itemsPerPage - 1) / itemsPerPage
}
