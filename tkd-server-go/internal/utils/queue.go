package utils

import (
	"context"
	"sync"
)

// Job 工作任务接口
type Job interface {
	Execute(ctx context.Context) (interface{}, error)
}

// JobFunc 函数类型的工作任务
type JobFunc func(ctx context.Context) (interface{}, error)

// Execute 实现Job接口
func (f JobFunc) Execute(ctx context.Context) (interface{}, error) {
	return f(ctx)
}

// WorkerPool 工作池
type WorkerPool struct {
	maxWorkers    int
	jobQueue      chan Job
	resultQueue   chan JobResult
	workers       []*Worker
	wg            sync.WaitGroup
	ctx           context.Context
	cancel        context.CancelFunc
	currentJobs   int
	mu            sync.RWMutex
}

// JobResult 工作结果
type JobResult struct {
	Result interface{}
	Error  error
	JobID  string
}

// Worker 工作者
type Worker struct {
	id         int
	jobQueue   chan Job
	resultQueue chan JobResult
	quit       chan bool
}

// NewWorkerPool 创建新的工作池
func NewWorkerPool(maxWorkers int) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	
	pool := &WorkerPool{
		maxWorkers:  maxWorkers,
		jobQueue:    make(chan Job, maxWorkers*2),
		resultQueue: make(chan JobResult, maxWorkers*2),
		workers:     make([]*Worker, maxWorkers),
		ctx:         ctx,
		cancel:      cancel,
	}

	// 启动工作者
	for i := 0; i < maxWorkers; i++ {
		worker := &Worker{
			id:          i,
			jobQueue:    pool.jobQueue,
			resultQueue: pool.resultQueue,
			quit:        make(chan bool),
		}
		pool.workers[i] = worker
		pool.wg.Add(1)
		go worker.start(&pool.wg, pool.ctx)
	}

	return pool
}

// Submit 提交工作任务
func (p *WorkerPool) Submit(job Job) <-chan JobResult {
	resultChan := make(chan JobResult, 1)
	
	go func() {
		defer close(resultChan)
		
		p.mu.Lock()
		p.currentJobs++
		p.mu.Unlock()
		
		defer func() {
			p.mu.Lock()
			p.currentJobs--
			p.mu.Unlock()
		}()
		
		select {
		case p.jobQueue <- job:
			// 等待结果
			select {
			case result := <-p.resultQueue:
				resultChan <- result
			case <-p.ctx.Done():
				resultChan <- JobResult{Error: p.ctx.Err()}
			}
		case <-p.ctx.Done():
			resultChan <- JobResult{Error: p.ctx.Err()}
		}
	}()
	
	return resultChan
}

// GetStatus 获取工作池状态
func (p *WorkerPool) GetStatus() (queueLength, currentJobs, maxWorkers int) {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	return len(p.jobQueue), p.currentJobs, p.maxWorkers
}

// Close 关闭工作池
func (p *WorkerPool) Close() {
	p.cancel()
	
	// 关闭所有工作者
	for _, worker := range p.workers {
		worker.quit <- true
	}
	
	p.wg.Wait()
	close(p.jobQueue)
	close(p.resultQueue)
}

// start 启动工作者
func (w *Worker) start(wg *sync.WaitGroup, ctx context.Context) {
	defer wg.Done()
	
	for {
		select {
		case job := <-w.jobQueue:
			result, err := job.Execute(ctx)
			w.resultQueue <- JobResult{
				Result: result,
				Error:  err,
			}
		case <-w.quit:
			return
		case <-ctx.Done():
			return
		}
	}
}
