package main

import (
	"log"
	"os"

	"tkd-server-go/internal/config"
	"tkd-server-go/internal/database"
	"tkd-server-go/internal/router"
	"tkd-server-go/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found")
	}

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Init()
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// 初始化服务
	services := services.NewServices(db, cfg)

	// 设置Gin模式
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化路由
	r := router.SetupRouter(services, cfg)

	// 启动服务器
	addr := cfg.IP + ":" + cfg.Port
	log.Printf("Server running at http://%s", addr)
	
	// 显示认证token（仅用于开发环境）
	if cfg.Environment != "production" && cfg.AuthToken != "" {
		log.Printf("Auth token: %s", cfg.AuthToken)
		log.Println("Use this token in the 'Authorization' header for POST /setting")
	} else if cfg.AuthToken == "" {
		log.Println("Warning: AUTH_TOKEN not configured in .env")
	}

	if err := r.Run(addr); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
