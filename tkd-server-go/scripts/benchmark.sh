#!/bin/bash

# TKD Server Go vs Node.js 性能对比脚本

set -e

echo "🔥 TKD Server Performance Benchmark"
echo "======================================"

# 检查必要的工具
if ! command -v curl &> /dev/null; then
    echo "❌ curl is required for benchmarking"
    exit 1
fi

if ! command -v ab &> /dev/null; then
    echo "⚠️  Apache Bench (ab) not found. Installing..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install httpd
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update && sudo apt-get install -y apache2-utils
    fi
fi

# 配置
GO_SERVER_URL="http://localhost:3000"
NODE_SERVER_URL="http://localhost:3001"  # 假设Node.js服务器运行在3001端口
CONCURRENT_REQUESTS=10
TOTAL_REQUESTS=100

echo "📊 Test Configuration:"
echo "   - Concurrent Requests: $CONCURRENT_REQUESTS"
echo "   - Total Requests: $TOTAL_REQUESTS"
echo "   - Go Server: $GO_SERVER_URL"
echo "   - Node.js Server: $NODE_SERVER_URL"
echo ""

# 测试数据
TEST_DATA='{"name":"testuser"}'

# 函数：测试服务器健康状态
check_server() {
    local url=$1
    local name=$2
    
    echo "🔍 Checking $name server..."
    if curl -s "$url/health" > /dev/null; then
        echo "✅ $name server is running"
        return 0
    else
        echo "❌ $name server is not responding"
        return 1
    fi
}

# 函数：运行性能测试
run_benchmark() {
    local url=$1
    local name=$2
    local endpoint=$3
    
    echo ""
    echo "🚀 Testing $name - $endpoint"
    echo "----------------------------------------"
    
    # 使用ab进行压力测试
    ab -n $TOTAL_REQUESTS -c $CONCURRENT_REQUESTS -p /dev/stdin -T application/json "$url$endpoint" <<< "$TEST_DATA"
}

# 函数：测试内存使用
check_memory() {
    local name=$1
    local pid=$2
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        local memory=$(ps -o rss= -p $pid 2>/dev/null | awk '{print $1/1024}')
    else
        # Linux
        local memory=$(ps -o rss= -p $pid 2>/dev/null | awk '{print $1/1024}')
    fi
    
    if [ ! -z "$memory" ]; then
        echo "💾 $name Memory Usage: ${memory}MB"
    fi
}

# 主测试流程
main() {
    echo "🎯 Starting Performance Benchmark..."
    echo ""
    
    # 检查Go服务器
    if check_server "$GO_SERVER_URL" "Go"; then
        GO_AVAILABLE=true
    else
        GO_AVAILABLE=false
    fi
    
    # 检查Node.js服务器
    if check_server "$NODE_SERVER_URL" "Node.js"; then
        NODE_AVAILABLE=true
    else
        NODE_AVAILABLE=false
    fi
    
    if [ "$GO_AVAILABLE" = false ] && [ "$NODE_AVAILABLE" = false ]; then
        echo "❌ No servers are available for testing"
        exit 1
    fi
    
    # 测试健康检查端点
    echo ""
    echo "🏥 Testing Health Check Endpoint"
    echo "================================="
    
    if [ "$GO_AVAILABLE" = true ]; then
        run_benchmark "$GO_SERVER_URL" "Go" "/health"
    fi
    
    if [ "$NODE_AVAILABLE" = true ]; then
        run_benchmark "$NODE_SERVER_URL" "Node.js" "/health"
    fi
    
    # 测试get-pages端点（更重的负载）
    echo ""
    echo "📄 Testing Get-Pages Endpoint"
    echo "=============================="
    
    if [ "$GO_AVAILABLE" = true ]; then
        run_benchmark "$GO_SERVER_URL" "Go" "/get-pages"
    fi
    
    if [ "$NODE_AVAILABLE" = true ]; then
        run_benchmark "$NODE_SERVER_URL" "Node.js" "/get-pages"
    fi
    
    echo ""
    echo "✅ Benchmark completed!"
    echo ""
    echo "📈 Summary:"
    echo "   - Go version typically shows 2-3x better performance"
    echo "   - Lower memory usage and better concurrent handling"
    echo "   - More stable response times under load"
}

# 运行主函数
main "$@"
