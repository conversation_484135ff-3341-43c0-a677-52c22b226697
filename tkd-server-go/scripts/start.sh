#!/bin/bash

# TKD Server Go 启动脚本

set -e

echo "🚀 Starting TKD Server Go..."

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21 or later."
    exit 1
fi

# 检查yt-dlp是否安装
if ! command -v yt-dlp &> /dev/null; then
    echo "⚠️  yt-dlp is not installed. Installing..."
    if command -v pip3 &> /dev/null; then
        pip3 install yt-dlp
    elif command -v pip &> /dev/null; then
        pip install yt-dlp
    else
        echo "❌ pip is not installed. Please install yt-dlp manually."
        exit 1
    fi
fi

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cat > .env << EOF
# 认证配置
AUTH_TOKEN=jtD2EWgRu1tbij

# 服务器配置
NODE_ENV=development
PORT=3000
IP=0.0.0.0

# 并发配置
MAX_CONCURRENT_REQUESTS=10
WORKER_POOL_SIZE=20
EOF
    echo "✅ Created .env file with default settings."
fi

# 下载依赖
echo "📦 Downloading dependencies..."
go mod tidy

# 构建应用
echo "🔨 Building application..."
go build -o tkd-server .

# 启动服务器
echo "🎉 Starting server..."
./tkd-server
