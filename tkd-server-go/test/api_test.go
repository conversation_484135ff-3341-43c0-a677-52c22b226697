package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"tkd-server-go/internal/config"
	"tkd-server-go/internal/database"
	"tkd-server-go/internal/router"
	"tkd-server-go/internal/services"

	"github.com/stretchr/testify/assert"
)

func setupTestRouter() *http.ServeMux {
	// 创建测试配置
	cfg := &config.Config{
		IP:                    "0.0.0.0",
		Port:                  "3000",
		Environment:           "test",
		AuthToken:             "test-token",
		MaxConcurrentRequests: 5,
		WorkerPoolSize:        10,
	}

	// 创建内存数据库
	db, _ := database.Init()

	// 创建服务
	services := services.NewServices(db, cfg)

	// 创建路由
	r := router.SetupRouter(services, cfg)

	// 转换为标准库的ServeMux（简化测试）
	mux := http.NewServeMux()
	mux.Handle("/", r)

	return mux
}

func TestHealthEndpoint(t *testing.T) {
	router := setupTestRouter()

	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "ok", response["status"])
}

func TestGetSettingsEndpoint(t *testing.T) {
	router := setupTestRouter()

	req, _ := http.NewRequest("GET", "/setting", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)
}

func TestSaveSettingsWithoutAuth(t *testing.T) {
	router := setupTestRouter()

	settings := map[string]interface{}{
		"key": "value",
	}
	jsonData, _ := json.Marshal(settings)

	req, _ := http.NewRequest("POST", "/setting", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 401, w.Code)
}

func TestSaveSettingsWithAuth(t *testing.T) {
	router := setupTestRouter()

	settings := map[string]interface{}{
		"key": "value",
	}
	jsonData, _ := json.Marshal(settings)

	req, _ := http.NewRequest("POST", "/setting", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "test-token")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)
}

func TestGetPagesEndpoint(t *testing.T) {
	router := setupTestRouter()

	requestBody := map[string]interface{}{
		"name": "testuser",
	}
	jsonData, _ := json.Marshal(requestBody)

	req, _ := http.NewRequest("POST", "/get-pages", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 注意：这个测试可能会失败，因为需要实际的yt-dlp命令
	// 在实际测试环境中，你可能需要mock这些外部依赖
	assert.True(t, w.Code == 200 || w.Code == 500)
}
